"use client"

import { useState } from "react"
import { BrandDropdown, type Brand } from "@/components/brand-dropdown"

const initialBrands: Brand[] = [
  {
    name: "NYX Professional...",
    active: true,
    messageCount: 1,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "<PERSON><PERSON>",
    active: false,
    messageCount: 8,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "La Roche-Posay",
    active: false,
    messageCount: 15,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "Maybelline Ne...",
    active: false,
    messageCount: 3,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "<PERSON><PERSON><PERSON> Natural...",
    active: false,
    messageCount: 7,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "EyeBuyDirect",
    active: false,
    messageCount: 5,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "THINK",
    active: false,
    messageCount: 2,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "Blueair",
    active: false,
    messageCount: 9,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "This is a Very Lo...",
    active: false,
    messageCount: 4,
    analyticsCount: null,
    expanded: false
  }
]

export default function Home() {
  const [brandStates, setBrandStates] = useState<Brand[]>(initialBrands)

  const handleBrandUpdate = (updatedBrands: Brand[]) => {
    setBrandStates(updatedBrands)
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200 flex-col flex">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
              AC
            </div>
            <span className="font-medium text-gray-900 text-sm">AC Social Command</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">AI-Powered Social Media</div>
        </div>

        {/* Brands List */}
        <BrandDropdown
          brands={brandStates}
          onBrandUpdate={handleBrandUpdate}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        {/* Main Dashboard Content */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-2xl w-full text-center">
            {/* Header */}
            <div className="mb-16">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">AC Social Command</h1>
              <p className="text-base text-gray-600 mb-1">
                Streamline social media responses across beauty and lifestyle brands with
              </p>
              <p className="text-base text-gray-600 mb-6">AI-powered assistance</p>
              <p className="text-sm text-gray-500">Powered by Artisan Council</p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-3 gap-12 max-w-lg mx-auto">
              {/* Beauty Brands */}
              <div className="text-center">
                <div className="text-sm font-medium text-gray-600 mb-3">Beauty Brands</div>
                <div className="text-4xl font-bold text-gray-900 mb-1">8</div>
                <div className="text-xs text-gray-500">Active Accounts</div>
              </div>

              {/* Pending Responses */}
              <div className="text-center">
                <div className="text-sm font-medium text-gray-600 mb-3">Pending Responses</div>
                <div className="text-4xl font-bold text-gray-900 mb-1">23</div>
                <div className="text-xs text-gray-500">Awaiting Review</div>
              </div>

              {/* AC AI Assistant */}
              <div className="text-center">
                <div className="text-sm font-medium text-gray-600 mb-3">AC AI Assistant</div>
                <div className="mb-1">
                  <span className="inline-block bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                    Ready
                  </span>
                </div>
                <div className="text-xs text-gray-500">Ready to Assist</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}